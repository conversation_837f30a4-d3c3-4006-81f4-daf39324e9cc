#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模型训练模块
提供各种机器学习模型的训练和评估功能
"""

import time
import os
import numpy as np
from pathlib import Path
from joblib import dump
from sklearn.metrics import (
    accuracy_score, roc_auc_score, confusion_matrix,
    classification_report, average_precision_score,
    precision_score, recall_score, f1_score,
    mean_squared_error, mean_absolute_error, r2_score
)
from sklearn.model_selection import train_test_split
from sklearn.tree import DecisionTreeClassifier, DecisionTreeRegressor
from sklearn.ensemble import RandomForestClassifier, RandomForestRegressor
from xgboost import XGBClassifier, XGBRegressor
from lightgbm import LGBMClassifier, LGBMRegressor
from catboost import CatBoostClassifier, CatBoostRegressor
from sklearn.linear_model import LogisticRegression, LinearRegression
from sklearn.svm import SVC, SVR
from sklearn.neighbors import KNeighborsClassifier, KNeighborsRegressor
from sklearn.naive_bayes import GaussianNB
from sklearn.neural_network import MLPClassifier, MLPRegressor

# 导入配置和GPU支持
try:
    from .config import CACHE_PATH, MODEL_DISPLAY_NAMES, RANDOM_SEED
    from .logger import get_logger
    from ..utils.gpu_manager import get_gpu_manager, get_gpu_params_for_model
except ImportError:
    # 使用默认配置
    PROJECT_ROOT = Path(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    CACHE_PATH = PROJECT_ROOT / 'cache'
    RANDOM_SEED = 42
    MODEL_DISPLAY_NAMES = {
        'DecisionTree': '决策树',
        'RandomForest': '随机森林',
        'XGBoost': 'XGBoost',
        'LightGBM': 'LightGBM',
        'CatBoost': 'CatBoost',
        'Logistic': '逻辑回归',
        'SVM': '支持向量机',
        'KNN': 'K近邻',
        'NaiveBayes': '朴素贝叶斯',
        'NeuralNet': '神经网络'
    }
    
    import logging
    def get_logger(name):
        return logging.getLogger(name)

logger = get_logger(__name__)

# 确保缓存目录存在
CACHE_PATH.mkdir(parents=True, exist_ok=True)

def detect_problem_type(y):
    """
    检测问题类型：分类还是回归

    Args:
        y: 目标变量

    Returns:
        str: 'classification' 或 'regression'
    """
    # 检查是否为连续值
    unique_values = np.unique(y)

    # 如果唯一值数量很少且都是整数，可能是分类问题
    if len(unique_values) <= 20 and all(isinstance(val, (int, np.integer)) for val in unique_values):
        return 'classification'

    # 如果有浮点数或唯一值很多，是回归问题
    if any(isinstance(val, (float, np.floating)) for val in unique_values) or len(unique_values) > 20:
        return 'regression'

    # 默认为分类
    return 'classification'

class ModelTrainer:
    """
    统一的模型训练器类
    """
    
    def __init__(self, model_name, model_class_clf=None, model_class_reg=None, default_params=None):
        """
        初始化模型训练器

        Args:
            model_name: 模型名称
            model_class_clf: 分类模型类
            model_class_reg: 回归模型类
            default_params: 默认参数
        """
        self.model_name = model_name
        self.model_class_clf = model_class_clf
        self.model_class_reg = model_class_reg
        self.default_params = default_params or {}
    
    def _apply_gpu_and_parallel_params(self, params, use_gpu=True, n_jobs=-1):
        """
        应用GPU和并行参数

        Args:
            params: 参数字典
            use_gpu: 是否使用GPU
            n_jobs: 并行作业数
        """
        # 使用GPU管理器获取GPU参数
        if use_gpu:
            try:
                gpu_manager = get_gpu_manager()
                gpu_params = get_gpu_params_for_model(self.model_name)

                if gpu_params:
                    params.update(gpu_params)
                    logger.info(f"[{self.model_name}] 启用GPU加速: {gpu_params}")
                else:
                    logger.info(f"[{self.model_name}] 该模型不支持GPU加速或GPU不可用")
            except Exception as e:
                logger.warning(f"[{self.model_name}] GPU配置失败: {e}，使用CPU模式")
        
        # 并行参数设置
        if n_jobs is not None:
            if self.model_name in ['RandomForest']:
                params['n_jobs'] = n_jobs
            elif self.model_name == 'XGBoost':
                if not use_gpu:
                    params['nthread'] = n_jobs if n_jobs > 0 else None
            elif self.model_name == 'LightGBM':
                params['n_jobs'] = n_jobs
            elif self.model_name in ['SVM', 'Logistic']:
                # 检查分类模型是否支持n_jobs
                if self.model_class_clf and hasattr(self.model_class_clf(), 'n_jobs'):
                    params['n_jobs'] = n_jobs
            # DecisionTree不支持n_jobs参数，跳过
    
    def train_and_evaluate(self, X_train, y_train, X_test, y_test, params=None,
                          use_gpu=True, n_jobs=-1):
        """
        训练和评估模型

        Args:
            X_train: 训练特征
            y_train: 训练标签
            X_test: 测试特征
            y_test: 测试标签
            params: 模型参数
            use_gpu: 是否使用GPU
            n_jobs: 并行作业数

        Returns:
            dict: 训练结果
        """
        start_time = time.time()
        logger.info(f"开始训练模型: {self.model_name}")

        # 检测问题类型
        problem_type = detect_problem_type(y_train)
        logger.info(f"检测到问题类型: {problem_type}")

        # 选择合适的模型类
        if problem_type == 'regression':
            if self.model_class_reg is None:
                raise ValueError(f"模型 {self.model_name} 不支持回归任务")
            model_class = self.model_class_reg
        else:
            if self.model_class_clf is None:
                raise ValueError(f"模型 {self.model_name} 不支持分类任务")
            model_class = self.model_class_clf

        # 合并参数
        final_params = self.default_params.copy()
        if params:
            final_params.update(params)

        # 应用GPU和并行参数
        self._apply_gpu_and_parallel_params(final_params, use_gpu, n_jobs)

        # 特殊处理XGBoost的eval_metric参数
        if self.model_name == 'XGBoost':
            if problem_type == 'regression':
                if 'eval_metric' in final_params and final_params['eval_metric'] == 'auto':
                    final_params['eval_metric'] = 'rmse'
            else:
                if 'eval_metric' in final_params and final_params['eval_metric'] == 'auto':
                    final_params['eval_metric'] = 'logloss'

        # 特殊处理SVM的probability参数（仅分类时有效）
        if self.model_name == 'SVM' and problem_type == 'regression':
            if 'probability' in final_params:
                del final_params['probability']

        try:
            # 创建并训练模型
            model = model_class(**final_params)
            
            # 特殊处理CatBoost的verbose参数
            if self.model_name == 'CatBoost':
                model.fit(X_train, y_train, verbose=False)
            else:
                model.fit(X_train, y_train)
            
            # 预测
            y_pred = model.predict(X_test)
            y_pred_proba = None
            
            # 获取预测概率（仅分类任务）
            if problem_type == 'classification':
                if hasattr(model, 'predict_proba'):
                    y_pred_proba = model.predict_proba(X_test)[:, 1]
                elif hasattr(model, 'decision_function'):
                    y_pred_proba = model.decision_function(X_test)

            # 计算评估指标
            metrics = self._calculate_metrics(y_test, y_pred, y_pred_proba, problem_type)
            
            # 训练时间
            training_time = time.time() - start_time
            
            # 保存特征名称
            feature_names = None
            if hasattr(X_train, 'columns'):
                feature_names = list(X_train.columns)
            
            # 构建结果
            result = {
                'model': model,
                'model_name': self.model_name,
                'y_true': y_test,
                'y_pred': y_pred,
                'y_pred_proba': y_pred_proba,
                'metrics': metrics,
                'training_time': training_time,
                'feature_names': feature_names,
                'X_test': X_test,
                'params': final_params
            }
            
            logger.info(f"模型 {self.model_name} 训练完成，用时 {training_time:.2f}秒")

            # 根据问题类型输出不同的指标
            if problem_type == 'regression':
                logger.info(f"R²: {metrics.get('r2', 'N/A'):.4f}, MSE: {metrics.get('mse', 'N/A'):.4f}")
            else:
                logger.info(f"准确率: {metrics.get('accuracy', 'N/A'):.4f}, AUC: {metrics.get('auc', 'N/A')}")
            
            # 缓存结果
            self._cache_result(result)
            
            return result
            
        except Exception as e:
            logger.error(f"训练模型 {self.model_name} 时出错: {e}")
            raise
    
    def _calculate_metrics(self, y_true, y_pred, y_pred_proba=None, problem_type='classification'):
        """
        计算评估指标

        Args:
            y_true: 真实标签
            y_pred: 预测标签
            y_pred_proba: 预测概率
            problem_type: 问题类型 ('classification' 或 'regression')

        Returns:
            dict: 评估指标
        """
        metrics = {}

        try:
            if problem_type == 'regression':
                # 回归指标
                metrics['mse'] = mean_squared_error(y_true, y_pred)
                metrics['rmse'] = np.sqrt(metrics['mse'])
                metrics['mae'] = mean_absolute_error(y_true, y_pred)
                metrics['r2'] = r2_score(y_true, y_pred)

                # 主要指标（用于模型比较）
                metrics['score'] = metrics['r2']

            else:
                # 分类指标
                metrics['accuracy'] = accuracy_score(y_true, y_pred)
                metrics['precision'] = precision_score(y_true, y_pred, average='weighted', zero_division=0)
                metrics['recall'] = recall_score(y_true, y_pred, average='weighted', zero_division=0)
                metrics['f1'] = f1_score(y_true, y_pred, average='weighted', zero_division=0)

                # 如果有预测概率，计算AUC和AP
                if y_pred_proba is not None:
                    try:
                        metrics['auc'] = roc_auc_score(y_true, y_pred_proba)
                        metrics['average_precision'] = average_precision_score(y_true, y_pred_proba)
                    except Exception as e:
                        logger.warning(f"计算AUC/AP时出错: {e}")

                # 主要指标（用于模型比较）
                metrics['score'] = metrics['accuracy']

        except Exception as e:
            logger.warning(f"计算评估指标时出错: {e}")
            if problem_type == 'regression':
                metrics['r2'] = 0.0
                metrics['score'] = 0.0
            else:
                metrics['accuracy'] = 0.0
                metrics['score'] = 0.0

        return metrics
    
    def _cache_result(self, result):
        """
        缓存训练结果
        
        Args:
            result: 训练结果
        """
        try:
            cache_file = CACHE_PATH / f"{self.model_name}_results.joblib"
            dump(result, cache_file)
            logger.info(f"模型 {self.model_name} 的结果已缓存到: {cache_file}")
            
            # 保存特征名称
            if result.get('feature_names'):
                feature_names_file = CACHE_PATH / f"{self.model_name}_feature_names.joblib"
                dump(result['feature_names'], feature_names_file)
                
        except Exception as e:
            logger.warning(f"缓存结果失败: {e}")

# 模型训练器实例
MODEL_TRAINERS = {
    'DecisionTree': ModelTrainer('DecisionTree',
                                model_class_clf=DecisionTreeClassifier,
                                model_class_reg=DecisionTreeRegressor,
                                default_params={
        'random_state': RANDOM_SEED,
        'max_depth': 5,
        'min_samples_split': 20,
        'min_samples_leaf': 10
    }),
    'RandomForest': ModelTrainer('RandomForest',
                                model_class_clf=RandomForestClassifier,
                                model_class_reg=RandomForestRegressor,
                                default_params={
        'random_state': RANDOM_SEED,
        'n_estimators': 100,
        'max_depth': 10,
        'min_samples_split': 10,
        'min_samples_leaf': 5
    }),
    'XGBoost': ModelTrainer('XGBoost',
                           model_class_clf=XGBClassifier,
                           model_class_reg=XGBRegressor,
                           default_params={
        'random_state': RANDOM_SEED,
        'n_estimators': 100,
        'learning_rate': 0.1,
        'max_depth': 6,
        'eval_metric': 'auto'  # 自动选择评估指标
    }),
    'LightGBM': ModelTrainer('LightGBM',
                            model_class_clf=LGBMClassifier,
                            model_class_reg=LGBMRegressor,
                            default_params={
        'random_state': RANDOM_SEED,
        'n_estimators': 100,
        'learning_rate': 0.1,
        'max_depth': 6,
        'verbosity': -1
    }),
    'CatBoost': ModelTrainer('CatBoost',
                            model_class_clf=CatBoostClassifier,
                            model_class_reg=CatBoostRegressor,
                            default_params={
        'random_state': RANDOM_SEED,
        'iterations': 100,
        'learning_rate': 0.1,
        'depth': 6,
        'verbose': False
    }),
    'Logistic': ModelTrainer('Logistic',
                            model_class_clf=LogisticRegression,
                            model_class_reg=LinearRegression,
                            default_params={
        'random_state': RANDOM_SEED,
        'max_iter': 1000
    }),
    'SVM': ModelTrainer('SVM',
                       model_class_clf=SVC,
                       model_class_reg=SVR,
                       default_params={
        'random_state': RANDOM_SEED,
        'probability': True  # 仅分类时有效
    }),
    'KNN': ModelTrainer('KNN',
                       model_class_clf=KNeighborsClassifier,
                       model_class_reg=KNeighborsRegressor,
                       default_params={
        'n_neighbors': 5
    }),
    'NaiveBayes': ModelTrainer('NaiveBayes',
                              model_class_clf=GaussianNB,
                              model_class_reg=None,
                              default_params={}),  # 朴素贝叶斯不支持回归
    'NeuralNet': ModelTrainer('NeuralNet',
                             model_class_clf=MLPClassifier,
                             model_class_reg=MLPRegressor,
                             default_params={
        'random_state': RANDOM_SEED,
        'max_iter': 2000,
        'hidden_layer_sizes': (100,)
    })
}

# 保持向后兼容的训练函数
def train_decision_tree(X_train, y_train, X_test, y_test, params=None):
    """训练决策树模型"""
    return MODEL_TRAINERS['DecisionTree'].train_and_evaluate(X_train, y_train, X_test, y_test, params)

def train_random_forest(X_train, y_train, X_test, y_test, params=None):
    """训练随机森林模型"""
    return MODEL_TRAINERS['RandomForest'].train_and_evaluate(X_train, y_train, X_test, y_test, params)

def train_xgboost(X_train, y_train, X_test, y_test, params=None):
    """训练XGBoost模型"""
    return MODEL_TRAINERS['XGBoost'].train_and_evaluate(X_train, y_train, X_test, y_test, params)

def train_lightgbm(X_train, y_train, X_test, y_test, params=None):
    """训练LightGBM模型"""
    return MODEL_TRAINERS['LightGBM'].train_and_evaluate(X_train, y_train, X_test, y_test, params)

def train_catboost(X_train, y_train, X_test, y_test, params=None):
    """训练CatBoost模型"""
    return MODEL_TRAINERS['CatBoost'].train_and_evaluate(X_train, y_train, X_test, y_test, params)

def train_logistic(X_train, y_train, X_test, y_test, params=None):
    """训练逻辑回归模型"""
    return MODEL_TRAINERS['Logistic'].train_and_evaluate(X_train, y_train, X_test, y_test, params)

def train_svm(X_train, y_train, X_test, y_test, params=None):
    """训练SVM模型"""
    return MODEL_TRAINERS['SVM'].train_and_evaluate(X_train, y_train, X_test, y_test, params)

def train_knn(X_train, y_train, X_test, y_test, params=None):
    """训练KNN模型"""
    return MODEL_TRAINERS['KNN'].train_and_evaluate(X_train, y_train, X_test, y_test, params)

def train_naive_bayes(X_train, y_train, X_test, y_test, params=None):
    """训练朴素贝叶斯模型"""
    return MODEL_TRAINERS['NaiveBayes'].train_and_evaluate(X_train, y_train, X_test, y_test, params)

def train_neural_net(X_train, y_train, X_test, y_test, params=None):
    """训练神经网络模型"""
    return MODEL_TRAINERS['NeuralNet'].train_and_evaluate(X_train, y_train, X_test, y_test, params)
