{"meta": {"test_sets": [], "test_metrics": [], "learn_metrics": [{"best_value": "Min", "name": "MultiClass"}], "launch_mode": "Train", "parameters": "", "iteration_count": 100, "learn_sets": ["learn"], "name": "experiment"}, "iterations": [{"learn": [4.940595573], "iteration": 0, "passed_time": 0.1631036945, "remaining_time": 16.14726575}, {"learn": [4.911571867], "iteration": 1, "passed_time": 0.2099882448, "remaining_time": 10.28942399}, {"learn": [4.881843282], "iteration": 2, "passed_time": 0.2582296387, "remaining_time": 8.349424984}, {"learn": [4.85121829], "iteration": 3, "passed_time": 0.3059921738, "remaining_time": 7.343812172}, {"learn": [4.821681823], "iteration": 4, "passed_time": 0.352352325, "remaining_time": 6.694694175}, {"learn": [4.794232217], "iteration": 5, "passed_time": 0.397216937, "remaining_time": 6.223065346}, {"learn": [4.764267534], "iteration": 6, "passed_time": 0.4461809757, "remaining_time": 5.927832963}, {"learn": [4.734713739], "iteration": 7, "passed_time": 0.4920113293, "remaining_time": 5.658130287}, {"learn": [4.704531147], "iteration": 8, "passed_time": 0.5419210575, "remaining_time": 5.479424025}, {"learn": [4.676008701], "iteration": 9, "passed_time": 0.5968880005, "remaining_time": 5.371992004}, {"learn": [4.650899855], "iteration": 10, "passed_time": 0.6442817273, "remaining_time": 5.212824885}, {"learn": [4.620993431], "iteration": 11, "passed_time": 0.69305712, "remaining_time": 5.08241888}, {"learn": [4.591176819], "iteration": 12, "passed_time": 0.7423510843, "remaining_time": 4.968041872}, {"learn": [4.564097401], "iteration": 13, "passed_time": 0.7892543609, "remaining_time": 4.848276788}, {"learn": [4.537399007], "iteration": 14, "passed_time": 0.8367710082, "remaining_time": 4.74170238}, {"learn": [4.510325877], "iteration": 15, "passed_time": 0.8842335767, "remaining_time": 4.642226278}, {"learn": [4.482516242], "iteration": 16, "passed_time": 0.9318436629, "remaining_time": 4.549589648}, {"learn": [4.454102613], "iteration": 17, "passed_time": 0.9797594852, "remaining_time": 4.463348766}, {"learn": [4.426515624], "iteration": 18, "passed_time": 1.026654211, "remaining_time": 4.376789003}, {"learn": [4.398198722], "iteration": 19, "passed_time": 1.074936428, "remaining_time": 4.299745713}, {"learn": [4.370817326], "iteration": 20, "passed_time": 1.12388973, "remaining_time": 4.227966129}, {"learn": [4.343249845], "iteration": 21, "passed_time": 1.171638551, "remaining_time": 4.153991225}, {"learn": [4.315941772], "iteration": 22, "passed_time": 1.21869927, "remaining_time": 4.079993207}, {"learn": [4.288294834], "iteration": 23, "passed_time": 1.266560317, "remaining_time": 4.010774338}, {"learn": [4.259624116], "iteration": 24, "passed_time": 1.315855986, "remaining_time": 3.947567959}, {"learn": [4.235059689], "iteration": 25, "passed_time": 1.363694777, "remaining_time": 3.881285134}, {"learn": [4.211198198], "iteration": 26, "passed_time": 1.387636184, "remaining_time": 3.751757089}, {"learn": [4.182518767], "iteration": 27, "passed_time": 1.434837563, "remaining_time": 3.689582304}, {"learn": [4.154304202], "iteration": 28, "passed_time": 1.481169235, "remaining_time": 3.626310887}, {"learn": [4.12748179], "iteration": 29, "passed_time": 1.530776727, "remaining_time": 3.571812364}, {"learn": [4.100019129], "iteration": 30, "passed_time": 1.585554289, "remaining_time": 3.529136966}, {"learn": [4.073629894], "iteration": 31, "passed_time": 1.634336454, "remaining_time": 3.472964965}, {"learn": [4.054063784], "iteration": 32, "passed_time": 1.683609596, "remaining_time": 3.418237666}, {"learn": [4.026311363], "iteration": 33, "passed_time": 1.732273631, "remaining_time": 3.362648813}, {"learn": [4.000285822], "iteration": 34, "passed_time": 1.779714901, "remaining_time": 3.305184816}, {"learn": [3.972443408], "iteration": 35, "passed_time": 1.826803352, "remaining_time": 3.247650404}, {"learn": [3.960519674], "iteration": 36, "passed_time": 1.831855155, "remaining_time": 3.119104724}, {"learn": [3.934680201], "iteration": 37, "passed_time": 1.877727587, "remaining_time": 3.063660799}, {"learn": [3.90788882], "iteration": 38, "passed_time": 1.926003784, "remaining_time": 3.012467457}, {"learn": [3.883165596], "iteration": 39, "passed_time": 1.972900494, "remaining_time": 2.959350741}, {"learn": [3.859191425], "iteration": 40, "passed_time": 2.019022807, "remaining_time": 2.905423064}, {"learn": [3.834308816], "iteration": 41, "passed_time": 2.068050628, "remaining_time": 2.855879439}, {"learn": [3.809399916], "iteration": 42, "passed_time": 2.114474323, "remaining_time": 2.802907824}, {"learn": [3.784818525], "iteration": 43, "passed_time": 2.160089349, "remaining_time": 2.749204626}, {"learn": [3.758679219], "iteration": 44, "passed_time": 2.20756619, "remaining_time": 2.698136455}, {"learn": [3.731877319], "iteration": 45, "passed_time": 2.256123567, "remaining_time": 2.648492883}, {"learn": [3.707906711], "iteration": 46, "passed_time": 2.303290118, "remaining_time": 2.597327154}, {"learn": [3.684131148], "iteration": 47, "passed_time": 2.349188992, "remaining_time": 2.544954742}, {"learn": [3.66014789], "iteration": 48, "passed_time": 2.396034851, "remaining_time": 2.493832191}, {"learn": [3.634262007], "iteration": 49, "passed_time": 2.446264932, "remaining_time": 2.446264932}, {"learn": [3.609310709], "iteration": 50, "passed_time": 2.493443114, "remaining_time": 2.395661031}, {"learn": [3.583777924], "iteration": 51, "passed_time": 2.54367778, "remaining_time": 2.348010258}, {"learn": [3.558539126], "iteration": 52, "passed_time": 2.596867941, "remaining_time": 2.302882891}, {"learn": [3.533264293], "iteration": 53, "passed_time": 2.649889787, "remaining_time": 2.257313522}, {"learn": [3.508846196], "iteration": 54, "passed_time": 2.702928929, "remaining_time": 2.211487305}, {"learn": [3.484578455], "iteration": 55, "passed_time": 2.756124398, "remaining_time": 2.165526312}, {"learn": [3.462425345], "iteration": 56, "passed_time": 2.804037372, "remaining_time": 2.115326438}, {"learn": [3.439663441], "iteration": 57, "passed_time": 2.851594188, "remaining_time": 2.064947515}, {"learn": [3.41585898], "iteration": 58, "passed_time": 2.899271713, "remaining_time": 2.014748139}, {"learn": [3.392485443], "iteration": 59, "passed_time": 2.948568548, "remaining_time": 1.965712365}, {"learn": [3.372608561], "iteration": 60, "passed_time": 2.994558422, "remaining_time": 1.914553745}, {"learn": [3.347385234], "iteration": 61, "passed_time": 3.041855038, "remaining_time": 1.864362765}, {"learn": [3.3231156], "iteration": 62, "passed_time": 3.090510004, "remaining_time": 1.815061431}, {"learn": [3.298755364], "iteration": 63, "passed_time": 3.137597461, "remaining_time": 1.764898572}, {"learn": [3.273944063], "iteration": 64, "passed_time": 3.18634225, "remaining_time": 1.71572275}, {"learn": [3.250451444], "iteration": 65, "passed_time": 3.233926248, "remaining_time": 1.665962007}, {"learn": [3.229306253], "iteration": 66, "passed_time": 3.2806032, "remaining_time": 1.615819486}, {"learn": [3.204434405], "iteration": 67, "passed_time": 3.328669781, "remaining_time": 1.566432838}, {"learn": [3.187200317], "iteration": 68, "passed_time": 3.378702896, "remaining_time": 1.517967968}, {"learn": [3.163369922], "iteration": 69, "passed_time": 3.425742643, "remaining_time": 1.468175418}, {"learn": [3.138487306], "iteration": 70, "passed_time": 3.472722573, "remaining_time": 1.418435981}, {"learn": [3.116995902], "iteration": 71, "passed_time": 3.519322904, "remaining_time": 1.368625574}, {"learn": [3.096601323], "iteration": 72, "passed_time": 3.571518007, "remaining_time": 1.320972414}, {"learn": [3.073943982], "iteration": 73, "passed_time": 3.622097739, "remaining_time": 1.272628935}, {"learn": [3.052999919], "iteration": 74, "passed_time": 3.67188951, "remaining_time": 1.22396317}, {"learn": [3.035663866], "iteration": 75, "passed_time": 3.719173246, "remaining_time": 1.174475762}, {"learn": [3.01467887], "iteration": 76, "passed_time": 3.766254172, "remaining_time": 1.124985012}, {"learn": [2.99409097], "iteration": 77, "passed_time": 3.812452341, "remaining_time": 1.07530707}, {"learn": [2.973770544], "iteration": 78, "passed_time": 3.86166907, "remaining_time": 1.026519626}, {"learn": [2.950706803], "iteration": 79, "passed_time": 3.908491435, "remaining_time": 0.9771228589}, {"learn": [2.929784366], "iteration": 80, "passed_time": 3.955203498, "remaining_time": 0.9277637836}, {"learn": [2.906929994], "iteration": 81, "passed_time": 4.002660327, "remaining_time": 0.8786327546}, {"learn": [2.885384515], "iteration": 82, "passed_time": 4.04962397, "remaining_time": 0.829441054}, {"learn": [2.86156893], "iteration": 83, "passed_time": 4.098889894, "remaining_time": 0.7807409322}, {"learn": [2.841814229], "iteration": 84, "passed_time": 4.14464253, "remaining_time": 0.7314075054}, {"learn": [2.819492093], "iteration": 85, "passed_time": 4.190730983, "remaining_time": 0.6822120205}, {"learn": [2.803976554], "iteration": 86, "passed_time": 4.239202241, "remaining_time": 0.633444013}, {"learn": [2.787041073], "iteration": 87, "passed_time": 4.286844647, "remaining_time": 0.5845697246}, {"learn": [2.764999799], "iteration": 88, "passed_time": 4.334661339, "remaining_time": 0.5357446598}, {"learn": [2.744516397], "iteration": 89, "passed_time": 4.381880939, "remaining_time": 0.4868756599}, {"learn": [2.723826462], "iteration": 90, "passed_time": 4.429987358, "remaining_time": 0.4381306178}, {"learn": [2.703694326], "iteration": 91, "passed_time": 4.478293422, "remaining_time": 0.3894168193}, {"learn": [2.681709689], "iteration": 92, "passed_time": 4.52516224, "remaining_time": 0.3406036094}, {"learn": [2.661076719], "iteration": 93, "passed_time": 4.574353335, "remaining_time": 0.2919800001}, {"learn": [2.645651185], "iteration": 94, "passed_time": 4.6208346, "remaining_time": 0.2432018211}, {"learn": [2.631411692], "iteration": 95, "passed_time": 4.667184289, "remaining_time": 0.194466012}, {"learn": [2.615099929], "iteration": 96, "passed_time": 4.715294626, "remaining_time": 0.1458338544}, {"learn": [2.599097126], "iteration": 97, "passed_time": 4.762797013, "remaining_time": 0.09719993905}, {"learn": [2.579856503], "iteration": 98, "passed_time": 4.811834353, "remaining_time": 0.0486043874}, {"learn": [2.562022542], "iteration": 99, "passed_time": 4.860192155, "remaining_time": 0}]}