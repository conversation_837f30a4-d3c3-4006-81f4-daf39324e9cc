#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
可视化管理器
负责数据和模型结果的可视化展示
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import logging
from typing import Dict, Any, Optional, List
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg, NavigationToolbar2Tk
import seaborn as sns

from ...core.base_gui import BaseGUI
from ...core.component_factory import get_component_factory
from ...core.event_manager import get_event_manager, EventTypes
from ...components.enhanced_chart_widgets import EnhancedChartWidget

# 设置matplotlib中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans', 'Arial']
plt.rcParams['axes.unicode_minus'] = False


class VisualizationManager(BaseGUI):
    """可视化管理器类"""

    def __init__(self, parent):
        """初始化可视化管理器"""
        self.current_data = None
        self.current_results = None
        self.training_results = None
        self.plot_widgets = {}
        self.current_figures = {}

        # 可视化配置
        self.plot_config = {
            'figsize': (10, 6),
            'dpi': 100,
            'style': 'seaborn-v0_8',
            'color_palette': 'Set2'
        }

        super().__init__(parent)

        # 订阅相关事件
        self._bind_events()

    def _setup_ui(self):
        """设置UI界面"""
        factory = get_component_factory()

        # 主框架
        if self.parent:
            self.main_frame = factory.create_frame(self.parent)
            self.main_frame.pack(fill='both', expand=True, padx=5, pady=5)
        else:
            self.main_frame = None
            return

        # 创建标签页容器
        self.notebook = factory.create_notebook(self.main_frame)
        self.notebook.pack(fill='both', expand=True)

        # 创建各个功能标签页
        self._create_data_visualization_tab()
        self._create_model_results_tab()
        self._create_model_comparison_tab()
        self._create_shap_analysis_tab()
        self._create_report_generation_tab()

    def _create_data_visualization_tab(self):
        """创建数据可视化标签页"""
        factory = get_component_factory()

        # 创建标签页框架
        data_viz_frame = factory.create_frame(self.notebook)
        self.notebook.add(data_viz_frame, text="📊 数据可视化")

        # 创建水平分割面板
        data_paned = ttk.PanedWindow(data_viz_frame, orient=tk.HORIZONTAL)
        data_paned.pack(fill='both', expand=True, padx=5, pady=5)

        # 左侧控制面板
        data_control_frame = factory.create_frame(data_paned)
        data_control_frame.pack(fill='both', expand=True)

        # 状态信息
        status_frame = factory.create_labelframe(data_control_frame, text="📈 数据状态")
        status_frame.pack(fill='x', padx=5, pady=5)

        self.data_status_label = factory.create_label(status_frame, text="等待数据加载...", style='info')
        self.data_status_label.pack(padx=10, pady=10)

        # 图表类型选择
        chart_frame = factory.create_labelframe(data_control_frame, text="📋 图表类型")
        chart_frame.pack(fill='x', padx=5, pady=5)

        self.data_chart_vars = {}
        chart_types = [
            ('histogram', '📊 直方图 - 数据分布'),
            ('scatter', '🔵 散点图 - 特征关系'),
            ('boxplot', '📦 箱线图 - 异常值检测'),
            ('correlation', '🔥 相关性热图'),
            ('pairplot', '🎯 特征对比图'),
            ('distribution', '📈 分布对比图')
        ]

        for chart_id, chart_name in chart_types:
            var = tk.BooleanVar()
            self.data_chart_vars[chart_id] = var
            cb = factory.create_checkbutton(chart_frame, text=chart_name, variable=var)
            cb.pack(anchor='w', padx=10, pady=2)

        # 默认选择几个常用图表
        self.data_chart_vars['histogram'].set(True)
        self.data_chart_vars['correlation'].set(True)

        # 生成按钮
        button_frame = factory.create_frame(data_control_frame)
        button_frame.pack(fill='x', padx=5, pady=10)

        self.generate_data_charts_btn = factory.create_button(
            button_frame, text="🚀 生成数据图表",
            command=self._generate_data_charts, style='primary'
        )
        self.generate_data_charts_btn.pack(fill='x', pady=5)
        self.generate_data_charts_btn.config(state='disabled')

        # 右侧图表显示区域
        chart_display_frame = factory.create_frame(data_paned)
        chart_display_frame.pack(fill='both', expand=True)

        # 图表容器
        self.data_chart_notebook = factory.create_notebook(chart_display_frame)
        self.data_chart_notebook.pack(fill='both', expand=True, padx=5, pady=5)

        # 添加到分割面板
        data_paned.add(data_control_frame, weight=1)
        data_paned.add(chart_display_frame, weight=3)

    def _create_model_results_tab(self):
        """创建模型结果标签页"""
        factory = get_component_factory()

        # 创建标签页框架
        model_results_frame = factory.create_frame(self.notebook)
        self.notebook.add(model_results_frame, text="🤖 模型结果")

        # 创建水平分割面板
        model_paned = ttk.PanedWindow(model_results_frame, orient=tk.HORIZONTAL)
        model_paned.pack(fill='both', expand=True, padx=5, pady=5)

        # 左侧控制面板
        model_control_frame = factory.create_frame(model_paned, style='card')
        model_control_frame.pack(fill='both', expand=True)

        # 状态信息
        status_frame = factory.create_labelframe(model_control_frame, text="🤖 模型状态")
        status_frame.pack(fill='x', padx=5, pady=5)

        self.model_status_label = factory.create_label(status_frame, text="等待模型训练...", style='info')
        self.model_status_label.pack(padx=10, pady=10)

        # 模型选择
        model_select_frame = factory.create_labelframe(model_control_frame, text="📋 模型选择")
        model_select_frame.pack(fill='x', padx=5, pady=5)

        self.selected_model_var = tk.StringVar()
        self.model_combobox = factory.create_combobox(
            model_select_frame,
            textvariable=self.selected_model_var,
            state='readonly'
        )
        self.model_combobox.pack(fill='x', padx=10, pady=10)
        self.model_combobox.bind('<<ComboboxSelected>>', self._on_model_selected)

        # 图表类型选择
        chart_frame = factory.create_labelframe(model_control_frame, text="📊 图表类型")
        chart_frame.pack(fill='x', padx=5, pady=5)

        self.model_chart_vars = {}
        model_chart_types = [
            ('roc_curve', 'ROC曲线'),
            ('confusion_matrix', '混淆矩阵'),
            ('feature_importance', '特征重要性'),
            ('learning_curve', '学习曲线'),
            ('precision_recall', 'PR曲线'),
            ('calibration', '校准曲线')
        ]

        for chart_id, chart_name in model_chart_types:
            var = tk.BooleanVar(value=True)
            self.model_chart_vars[chart_id] = var
            cb = factory.create_checkbutton(chart_frame, text=chart_name, variable=var)
            cb.pack(anchor='w', padx=10, pady=2)

        # 生成按钮
        button_frame = factory.create_frame(model_control_frame)
        button_frame.pack(fill='x', padx=5, pady=10)

        self.generate_model_charts_btn = factory.create_button(
            button_frame, text="🚀 生成模型图表",
            command=self._generate_model_charts, style='primary'
        )
        self.generate_model_charts_btn.pack(fill='x', pady=5)
        self.generate_model_charts_btn.config(state='disabled')

        # 右侧图表显示区域
        model_chart_display_frame = factory.create_frame(model_paned)
        model_chart_display_frame.pack(fill='both', expand=True)

        # 创建图表显示的notebook
        self.model_chart_notebook = factory.create_notebook(model_chart_display_frame)
        self.model_chart_notebook.pack(fill='both', expand=True, padx=5, pady=5)

        # 添加到分割面板
        model_paned.add(model_control_frame, weight=1)
        model_paned.add(model_chart_display_frame, weight=3)

    def _create_model_comparison_tab(self):
        """创建模型比较标签页"""
        factory = get_component_factory()

        # 创建标签页框架
        comparison_frame = factory.create_frame(self.notebook)
        self.notebook.add(comparison_frame, text="🔍 模型比较")

        # 占位符内容
        placeholder_label = factory.create_label(
            comparison_frame,
            text="模型比较功能\n\n包括：\n• 性能指标对比\n• ROC曲线比较\n• 特征重要性对比\n• 训练时间比较\n\n请先完成多个模型的训练",
            style='secondary'
        )
        placeholder_label.pack(expand=True)

        # ——— 在模型比较页添加最小可用的结果表与按钮 ———
        try:
            controls = factory.create_frame(comparison_frame)
            controls.pack(fill='x', padx=10, pady=5)

            refresh_btn = factory.create_button(controls, text="🔄 刷新比较", command=self._refresh_comparison, style='secondary')
            refresh_btn.pack(side='left')

            run_delong_btn = factory.create_button(controls, text="🧪 DeLong检验", command=self._open_delong_window, style='default')
            run_delong_btn.pack(side='left', padx=6)

            # 简单的比较表格：展示各模型的核心指标
            from tkinter import ttk
            table_frame = factory.create_frame(comparison_frame)
            table_frame.pack(fill='both', expand=True, padx=10, pady=10)

            columns = ("模型", "准确率", "F1", "AUC", "训练时长(s)")
            self.comparison_tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=12)
            for col in columns:
                self.comparison_tree.heading(col, text=col)
                width = 120 if col != "模型" else 160
                anchor = 'center' if col != "模型" else 'w'
                self.comparison_tree.column(col, width=width, anchor=anchor)

            yscroll = ttk.Scrollbar(table_frame, orient='vertical', command=self.comparison_tree.yview)
            self.comparison_tree.configure(yscrollcommand=yscroll.set)

            self.comparison_tree.pack(side='left', fill='both', expand=True)
            yscroll.pack(side='right', fill='y')
        except Exception as _e:
            # 若任何组件创建失败，不影响原有占位内容
            pass


    def _create_shap_analysis_tab(self):
        """创建SHAP分析标签页"""
        factory = get_component_factory()

        # 创建标签页框架
        shap_frame = factory.create_frame(self.notebook)
        self.notebook.add(shap_frame, text="🧠 SHAP分析")

        # 占位符内容
        placeholder_label = factory.create_label(
            shap_frame,
            text="SHAP可解释性分析\n\n包括：\n• SHAP值计算\n• 特征贡献分析\n• 样本解释\n• 全局特征重要性\n\n请先完成模型训练",
            style='secondary'
        )
        placeholder_label.pack(expand=True)

    def _create_report_generation_tab(self):
        """创建报告生成标签页"""
        factory = get_component_factory()

        # 创建标签页框架
        report_frame = factory.create_frame(self.notebook)
        self.notebook.add(report_frame, text="📄 报告生成")

        # 占位符内容
        placeholder_label = factory.create_label(
            report_frame,
            text="自动报告生成\n\n包括：\n• HTML报告\n• PDF报告\n• 图表导出\n• 结果摘要\n\n请先完成模型训练和可视化",
            style='secondary'
        )
        placeholder_label.pack(expand=True)

    def _create_custom_charts_tab(self):
        """创建自定义图表标签页"""
        factory = get_component_factory()

        # 创建标签页框架
        custom_frame = factory.create_frame(self.notebook)
        self.notebook.add(custom_frame, text="自定义图表")

        # 自定义设置
        custom_control_frame = factory.create_labelframe(custom_frame, text="自定义设置", style='section')
        custom_control_frame.pack(fill='x', padx=10, pady=10)

        # 图表标题
        title_frame = factory.create_frame(custom_control_frame)
        title_frame.pack(fill='x', pady=5)

        factory.create_label(title_frame, text="图表标题:").pack(side='left')
        self.chart_title_var = factory.create_entry(title_frame)
        self.chart_title_var.pack(side='left', fill='x', expand=True, padx=(10, 0))

        # 颜色主题
        theme_frame = factory.create_frame(custom_control_frame)
        theme_frame.pack(fill='x', pady=5)

        factory.create_label(theme_frame, text="颜色主题:").pack(side='left')
        self.color_theme_var = factory.create_combobox(
            theme_frame,
            values=['默认', '蓝色系', '绿色系', '红色系', '紫色系'],
            state='readonly'
        )
        self.color_theme_var.pack(side='left', padx=(10, 0))
        self.color_theme_var.set('默认')

        # 图表大小
        size_frame = factory.create_frame(custom_control_frame)
        size_frame.pack(fill='x', pady=5)

        factory.create_label(size_frame, text="图表大小:").pack(side='left')
        self.chart_width_var = factory.create_spinbox(size_frame, from_=400, to=1200, increment=50)
        self.chart_width_var.pack(side='left', padx=(10, 5))
        self.chart_width_var.delete(0, 'end')
        self.chart_width_var.insert(0, '800')

        factory.create_label(size_frame, text="x").pack(side='left', padx=5)
        self.chart_height_var = factory.create_spinbox(size_frame, from_=300, to=800, increment=50)
        self.chart_height_var.pack(side='left', padx=5)
        self.chart_height_var.delete(0, 'end')
        self.chart_height_var.insert(0, '600')

        # 生成按钮
        custom_button_frame = factory.create_frame(custom_control_frame)
        custom_button_frame.pack(fill='x', pady=5)

        self.generate_custom_button = factory.create_button(
            custom_button_frame, text="生成自定义图表", command=self._generate_custom_chart, style='primary'
        )
        self.generate_custom_button.pack(side='left')
        self.generate_custom_button.config(state='disabled')

        self.save_chart_button = factory.create_button(
            custom_button_frame, text="保存图表", command=self._save_chart, style='secondary'
        )
        self.save_chart_button.pack(side='left', padx=(10, 0))
        self.save_chart_button.config(state='disabled')

        # 自定义图表显示区域
        custom_display_frame = factory.create_labelframe(custom_frame, text="自定义图表", style='section')
        custom_display_frame.pack(fill='both', expand=True, padx=10, pady=10)
        # 自定义图表显示区域
        self.custom_chart_widget = EnhancedChartWidget(custom_frame, figsize=(10, 6))
        self.plot_widgets['custom'] = self.custom_chart_widget

        # 图表组件已经包含了自己的滚动和导航功能

    def _bind_events(self):
        """绑定事件"""
        # 订阅数据和模型相关事件
        event_manager = get_event_manager()
        event_manager.subscribe(EventTypes.DATA_LOADED, self._on_data_loaded)
        event_manager.subscribe(EventTypes.DATA_PREPROCESSED, self._on_data_preprocessed)
        event_manager.subscribe(EventTypes.MODEL_TRAINED, self._on_model_trained)
        event_manager.subscribe(EventTypes.VISUALIZATION_REQUESTED, self._on_visualization_requested)

    def _generate_exploration_chart(self):
        """生成数据探索图表"""
        if self.current_data is None:
            self.show_warning("警告", "请先加载数据")
            return

        try:
            chart_type = self.chart_type_var.get()
            x_column = self.x_column_var.get()
            y_column = self.y_column_var.get() if self.y_column_var.get() else None

            if not x_column:
                self.show_warning("警告", "请选择X轴列")
                return

            # 使用增强图表组件生成图表
            success = self.exploration_chart_widget.create_data_exploration_chart(
                data=self.current_data,
                chart_type=chart_type,
                x_col=x_column,
                y_col=y_column
            )

            if success:
                self.show_info("成功", f"已生成{chart_type}图表")
            else:
                self.show_error("错误", "图表生成失败")

        except Exception as e:
            self.show_error("错误", f"生成图表时出错: {str(e)}")

    def _generate_result_chart(self):
        """生成模型结果图表"""
        if self.current_results is None:
            self.show_warning("警告", "请先训练模型")
            return

        try:
            result_type = self.result_type_var.get()

            # 使用增强图表组件生成模型结果图表
            success = self.result_chart_widget.create_model_result_chart(
                result_type=result_type,
                model_results=self.current_results
            )

            if success:
                self.show_info("成功", f"已生成{result_type}图表")
            else:
                self.show_error("错误", "模型结果图表生成失败")

        except Exception as e:
            self.show_error("错误", f"生成结果图表时出错: {str(e)}")

    def _generate_custom_chart(self):
        """生成自定义图表"""
        if self.current_data is None:
            self.show_warning("警告", "请先加载数据")
            return

        try:
            title = self.chart_title_var.get() or "自定义图表"
            theme = self.color_theme_var.get()
            width = self.chart_width_var.get() or "800"
            height = self.chart_height_var.get() or "600"

            # 使用默认的数据探索图表作为自定义图表
            numeric_cols = self.current_data.select_dtypes(include=['number']).columns
            if len(numeric_cols) > 0:
                success = self.custom_chart_widget.create_data_exploration_chart(
                    data=self.current_data,
                    chart_type='correlation',
                    x_col=numeric_cols[0]
                )
            else:
                success = False

            if success:
                self.save_chart_button.config(state='normal')
                self.show_info("成功", "已生成自定义图表")
            else:
                self.show_error("错误", "自定义图表生成失败")

        except Exception as e:
            self.show_error("错误", f"生成自定义图表时出错: {str(e)}")

    def _generate_chart_description(self, chart_type: str, x_column: str, y_column: str = None) -> str:
        """生成图表描述（模拟实际可视化）"""
        if self.current_data is None:
            return "无数据可显示"

        data_info = f"数据形状: {self.current_data.shape}\n"
        data_info += f"X轴列: {x_column}\n"
        if y_column:
            data_info += f"Y轴列: {y_column}\n"

        if chart_type == '直方图':
            if x_column in self.current_data.columns:
                values = self.current_data[x_column]
                if pd.api.types.is_numeric_dtype(values):
                    stats = f"""
{chart_type} - {x_column}
{data_info}
统计信息:
- 均值: {values.mean():.2f}
- 标准差: {values.std():.2f}
- 最小值: {values.min():.2f}
- 最大值: {values.max():.2f}
- 中位数: {values.median():.2f}

分布特征:
- 数据点数: {len(values)}
- 非空值: {values.count()}
- 缺失值: {values.isnull().sum()}

注: 实际应用中这里会显示直方图可视化
"""
                else:
                    unique_vals = values.value_counts()
                    stats = f"""
{chart_type} - {x_column} (分类数据)
{data_info}
分类统计:
{unique_vals.head(10).to_string()}

注: 实际应用中这里会显示条形图可视化
"""
                return stats

        elif chart_type == '散点图' and y_column:
            if x_column in self.current_data.columns and y_column in self.current_data.columns:
                x_vals = self.current_data[x_column]
                y_vals = self.current_data[y_column]

                if pd.api.types.is_numeric_dtype(x_vals) and pd.api.types.is_numeric_dtype(y_vals):
                    correlation = x_vals.corr(y_vals)
                    return f"""
{chart_type} - {x_column} vs {y_column}
{data_info}
相关性分析:
- 相关系数: {correlation:.3f}
- X轴范围: {x_vals.min():.2f} ~ {x_vals.max():.2f}
- Y轴范围: {y_vals.min():.2f} ~ {y_vals.max():.2f}

注: 实际应用中这里会显示散点图可视化
"""

        elif chart_type == '相关性热图':
            numeric_cols = self.current_data.select_dtypes(include=[np.number]).columns
            if len(numeric_cols) > 1:
                corr_matrix = self.current_data[numeric_cols].corr()
                return f"""
{chart_type}
{data_info}
数值列相关性矩阵:
{corr_matrix.round(3).to_string()}

注: 实际应用中这里会显示热图可视化
"""

        return f"""
{chart_type}
{data_info}
图表类型: {chart_type}
选择的列: {x_column}
{f'Y轴列: {y_column}' if y_column else ''}

注: 实际应用中这里会显示相应的可视化图表
"""

    def _generate_result_description(self, result_type: str) -> str:
        """生成结果描述（模拟实际可视化）"""
        if self.current_results is None:
            return "无模型结果可显示"

        base_info = f"结果类型: {result_type}\n"
        base_info += f"模型数量: {len(self.current_results.get('models', {}))}\n"

        if result_type == '模型性能对比':
            models_info = ""
            for model_name, model_data in self.current_results.get('models', {}).items():
                if 'metrics' in model_data:
                    metrics = model_data['metrics']
                    models_info += f"\n{model_name}:\n"
                    for metric, value in metrics.items():
                        models_info += f"  - {metric}: {value:.4f}\n"

            return f"""
{result_type}
{base_info}
性能指标对比:
{models_info}

注: 实际应用中这里会显示性能对比柱状图
"""

        elif result_type == '特征重要性':
            return f"""
{result_type}
{base_info}
特征重要性分析:
- 显示各特征对模型预测的重要程度
- 帮助理解模型决策过程
- 支持特征选择和模型优化

注: 实际应用中这里会显示特征重要性条形图
"""

        return f"""
{result_type}
{base_info}
结果可视化类型: {result_type}

注: 实际应用中这里会显示相应的结果可视化图表
"""

    def _generate_custom_description(self, title: str, theme: str, width: str, height: str) -> str:
        """生成自定义图表描述"""
        return f"""
自定义图表设置
==================
标题: {title}
颜色主题: {theme}
图表大小: {width} x {height}
数据源: {self.current_data.shape if self.current_data is not None else '无数据'}

自定义配置:
- 支持多种图表类型
- 可调整颜色主题
- 自定义图表尺寸
- 灵活的数据展示

注: 实际应用中这里会显示根据设置生成的自定义图表
"""

    def _clear_exploration_chart(self):
        """清空探索图表"""
        self.exploration_chart_widget.clear_chart()

    def _clear_result_chart(self):
        """清空结果图表"""
        self.result_chart_widget.clear_chart()

    def _save_chart(self):
        """保存图表"""
        try:
            from tkinter import filedialog
            filename = filedialog.asksaveasfilename(
                title="保存图表",
                defaultextension=".png",
                filetypes=[("PNG文件", "*.png"), ("JPG文件", "*.jpg"), ("所有文件", "*.*")]
            )

            if filename:
                # 模拟保存图表
                self.show_info("成功", f"图表已保存到: {filename}")

        except Exception as e:
            self.show_error("错误", f"保存图表时出错: {str(e)}")

    def _on_data_loaded(self, event_data):
        """数据加载事件处理"""
        try:
            # 更新当前数据
            if 'dataframe' in event_data:
                self.current_data = event_data['dataframe']

                # 更新列选择器
                columns = list(self.current_data.columns)
                self.x_column_var['values'] = columns
                self.y_column_var['values'] = [''] + columns

                # 启用按钮
                self.generate_chart_button.config(state='normal')
                self.generate_custom_button.config(state='normal')

                self.logger.info(f"可视化模块已加载数据: {self.current_data.shape}")

        except Exception as e:
            self.logger.error(f"处理数据加载事件时出错: {e}")

    def _on_model_trained(self, event_data):
        """模型训练事件处理"""
        try:
            # 更新当前结果
            self.current_results = event_data

            # 启用结果图表按钮
            self.generate_result_button.config(state='normal')

            self.logger.info("可视化模块已接收模型训练结果")

        except Exception as e:
            self.logger.error(f"处理模型训练事件时出错: {e}")

    def _on_visualization_requested(self, event_data):
        """可视化请求事件处理"""
        try:
            viz_type = event_data.get('type', 'data_exploration')

            if viz_type == 'data_exploration':
                self.notebook.select(0)  # 切换到数据探索标签页
            elif viz_type == 'model_results':
                self.notebook.select(1)  # 切换到模型结果标签页
            elif viz_type == 'custom':
                self.notebook.select(2)  # 切换到自定义图表标签页

        except Exception as e:
            self.logger.error(f"处理可视化请求事件时出错: {e}")

    def _generate_data_charts(self):
        """生成数据图表"""
        if self.current_data is None or (hasattr(self.current_data, 'empty') and self.current_data.empty):
            self.show_warning("警告", "请先加载数据！")
            return

        # 获取选中的图表类型
        selected_charts = [chart_id for chart_id, var in self.data_chart_vars.items() if var.get()]
        if not selected_charts:
            self.show_warning("警告", "请至少选择一种图表类型！")
            return

        try:
            # 清空现有图表
            for widget in self.data_chart_notebook.winfo_children():
                widget.destroy()

            # 生成选中的图表
            for chart_id in selected_charts:
                self._create_data_chart(chart_id)

            self.show_info("成功", f"已生成 {len(selected_charts)} 个数据图表")

        except Exception as e:
            self.show_error("错误", f"生成图表时出错: {e}")

    def _create_data_chart(self, chart_type):
        """创建具体的数据图表"""
        try:
            # 创建图表框架
            chart_frame = tk.Frame(self.data_chart_notebook)

            # 创建matplotlib图形
            fig, ax = plt.subplots(figsize=self.plot_config['figsize'], dpi=self.plot_config['dpi'])

            if chart_type == 'histogram':
                # 直方图 - 显示数值列的分布
                numeric_cols = self.current_data.select_dtypes(include=[np.number]).columns
                if len(numeric_cols) > 0:
                    col = numeric_cols[0]  # 选择第一个数值列
                    ax.hist(self.current_data[col].dropna(), bins=30, alpha=0.7, edgecolor='black')
                    ax.set_title(f'{col} 分布直方图')
                    ax.set_xlabel(col)
                    ax.set_ylabel('频次')

            elif chart_type == 'correlation':
                # 相关性热图
                numeric_data = self.current_data.select_dtypes(include=[np.number])
                if len(numeric_data.columns) > 1:
                    corr_matrix = numeric_data.corr()
                    im = ax.imshow(corr_matrix, cmap='coolwarm', aspect='auto', vmin=-1, vmax=1)
                    ax.set_xticks(range(len(corr_matrix.columns)))
                    ax.set_yticks(range(len(corr_matrix.columns)))
                    ax.set_xticklabels(corr_matrix.columns, rotation=45, ha='right')
                    ax.set_yticklabels(corr_matrix.columns)
                    ax.set_title('特征相关性热图')

                    # 添加颜色条
                    plt.colorbar(im, ax=ax)

                    # 添加数值标注
                    for i in range(len(corr_matrix.columns)):
                        for j in range(len(corr_matrix.columns)):
                            text = ax.text(j, i, f'{corr_matrix.iloc[i, j]:.2f}',
                                         ha="center", va="center", color="black", fontsize=8)

            plt.tight_layout()

            # 将图表嵌入到tkinter中
            canvas = FigureCanvasTkAgg(fig, chart_frame)
            canvas.draw()
            canvas.get_tk_widget().pack(fill='both', expand=True)

            # 添加导航工具栏
            toolbar = NavigationToolbar2Tk(canvas, chart_frame)
            toolbar.update()

            # 添加到notebook
            chart_names = {
                'histogram': '📊 直方图',
                'correlation': '🔥 相关性热图',
                'scatter': '🔵 散点图',
                'boxplot': '📦 箱线图',
                'pairplot': '🎯 特征对比图',
                'distribution': '📈 分布对比图'
            }

            self.data_chart_notebook.add(chart_frame, text=chart_names.get(chart_type, chart_type))

        except Exception as e:
            self.logger.error(f"创建 {chart_type} 图表时出错: {e}")
            raise

    def get_current_data(self) -> Optional[pd.DataFrame]:
        """获取当前数据"""
        return self.current_data

    def get_current_results(self) -> Optional[Dict[str, Any]]:
        """获取当前结果"""
        return self.current_results



    def _on_data_loaded(self, event_data):
        """数据加载事件处理"""
        try:
            if event_data and 'data' in event_data:
                self.current_data = event_data['data']

                # 更新状态
                rows, cols = self.current_data.shape
                self.data_status_label.config(text=f"✅ 数据已加载: {rows} 行, {cols} 列")

                # 启用数据图表生成按钮
                self.generate_data_charts_btn.config(state='normal')

                self.logger.info(f"可视化模块已加载数据: {self.current_data.shape}")
            else:
                self.logger.warning("数据加载事件中缺少'data'字段")
                self.data_status_label.config(text="❌ 数据加载失败")
        except Exception as e:
            self.logger.error(f"处理数据加载事件时出错: {e}")
            self.data_status_label.config(text="❌ 数据加载失败")

    def _on_data_preprocessed(self, event_data):
        """数据预处理事件处理"""
        try:
            if event_data and 'data' in event_data:
                # 如果有预处理后的数据，更新当前数据
                self.current_data = event_data['data']
                rows, cols = self.current_data.shape
                self.data_status_label.config(text=f"✅ 数据已预处理: {rows} 行, {cols} 列")
                self.generate_data_charts_btn.config(state='normal')
                self.logger.info(f"可视化模块已接收预处理数据: {self.current_data.shape}")
        except Exception as e:
            self.logger.error(f"处理数据预处理事件时出错: {e}")

    def _on_model_trained(self, event_data):
        """模型训练完成事件处理"""
        try:
            if event_data and 'results' in event_data:
                self.training_results = event_data['results']

                # 更新模型状态
                model_count = len(self.training_results)
                self.model_status_label.config(text=f"✅ 已训练 {model_count} 个模型")

                # 更新模型选择下拉框
                model_names = list(self.training_results.keys())
                self.model_combobox['values'] = model_names
                if model_names:
                    self.model_combobox.set(model_names[0])

                # 启用模型图表生成按钮
                self.generate_model_charts_btn.config(state='normal')

                self.logger.info(f"可视化模块已接收训练结果: {model_count} 个模型")
        except Exception as e:
            self.logger.error(f"处理模型训练事件时出错: {e}")

    def _on_model_selected(self, event):
        """模型选择事件处理"""

    def _refresh_comparison(self):
        """刷新模型比较表（从训练结果中读取指标）"""
        try:
            if not hasattr(self, 'comparison_tree') or not self.training_results:
                self.show_info("提示", "请先完成多个模型的训练，再进行比较。")
                return
            # 清空
            for item in self.comparison_tree.get_children():
                self.comparison_tree.delete(item)
            # 填充
            for model_name, result in self.training_results.items():
                metrics = result.get('metrics', result)
                acc = metrics.get('accuracy') or metrics.get('acc') or 0
                f1 = metrics.get('f1') or metrics.get('f1_score') or 0
                auc = metrics.get('auc') or metrics.get('roc_auc') or 0
                t = result.get('training_time', metrics.get('training_time', 0))
                self.comparison_tree.insert('', 'end', values=(model_name, f"{acc:.4f}", f"{f1:.4f}", f"{auc:.4f}", f"{t:.2f}"))
        except Exception as e:
            self.logger.error(f"刷新模型比较失败: {e}")

    def _open_delong_window(self):
        """从可视化页打开 DeLong 检验窗口"""
        try:
            import tkinter as tk
            from ..analysis.delong_test_gui import DeLongTestGUI
            top = tk.Toplevel(self.parent)
            top.title("DeLong 检验")
            top.geometry("900x650")
            self._delong_gui = DeLongTestGUI(top)
        except Exception as e:
            self.show_warning("提示", f"无法打开 DeLong 检验窗口：{e}")

        selected_model = self.selected_model_var.get()
        if selected_model and selected_model in self.training_results:
            # 可以在这里更新模型相关的显示信息
            pass

    def _on_visualization_requested(self, event_data):
        """可视化请求事件处理"""
        try:
            if event_data:
                chart_type = event_data.get('chart_type', 'default')
                model_name = event_data.get('model_name', None)

                if chart_type == 'model_results' and model_name:
                    # 切换到模型结果标签页并生成图表
                    self.notebook.select(1)  # 模型结果标签页
                    if model_name in self.training_results:
                        self.model_combobox.set(model_name)
                        self._generate_model_charts()
                elif chart_type == 'data_exploration':
                    # 切换到数据探索标签页
                    self.notebook.select(0)  # 数据探索标签页
                    self._generate_exploration_chart()

                self.logger.info(f"处理可视化请求: {chart_type}")
        except Exception as e:
            self.logger.error(f"处理可视化请求时出错: {e}")

    def _generate_model_charts(self):
        """生成模型图表"""
        if not self.training_results:
            self.show_warning("警告", "请先完成模型训练！")
            return

        selected_model = self.selected_model_var.get()
        if not selected_model:
            self.show_warning("警告", "请选择要分析的模型！")
            return

        if selected_model not in self.training_results:
            self.show_warning("警告", f"模型 {selected_model} 不存在！")
            return

        # 获取选中的图表类型
        selected_charts = [chart_id for chart_id, var in self.model_chart_vars.items() if var.get()]
        if not selected_charts:
            self.show_warning("警告", "请至少选择一种图表类型！")
            return

        try:
            # 清空现有图表
            for widget in self.model_chart_notebook.winfo_children():
                widget.destroy()

            # 获取模型结果
            model_result = self.training_results[selected_model]

            # 生成选中的图表
            for chart_id in selected_charts:
                self._create_model_chart(chart_id, selected_model, model_result)

            self.show_info("成功", f"已为模型 {selected_model} 生成 {len(selected_charts)} 个图表")

        except Exception as e:
            self.show_error("错误", f"生成模型图表时出错: {e}")

    def _create_model_chart(self, chart_type, model_name, model_result):
        """创建具体的模型图表"""
        try:
            # 创建图表框架
            chart_frame = tk.Frame(self.model_chart_notebook)

            # 创建matplotlib图形
            fig, ax = plt.subplots(figsize=self.plot_config['figsize'], dpi=self.plot_config['dpi'])

            if chart_type == 'roc_curve':
                # ROC曲线
                if 'metrics' in model_result and 'fpr' in model_result['metrics'] and 'tpr' in model_result['metrics']:
                    fpr = model_result['metrics']['fpr']
                    tpr = model_result['metrics']['tpr']
                    auc = model_result['metrics'].get('auc', 0)

                    ax.plot(fpr, tpr, label=f'{model_name} (AUC = {auc:.3f})')
                    ax.plot([0, 1], [0, 1], 'k--', label='随机分类器')
                    ax.set_xlabel('假正率 (FPR)')
                    ax.set_ylabel('真正率 (TPR)')
                    ax.set_title(f'{model_name} ROC曲线')
                    ax.legend()
                    ax.grid(True, alpha=0.3)
                else:
                    ax.text(0.5, 0.5, 'ROC曲线数据不可用', ha='center', va='center', transform=ax.transAxes)
                    ax.set_title(f'{model_name} ROC曲线')

            elif chart_type == 'confusion_matrix':
                # 混淆矩阵
                if 'metrics' in model_result and 'confusion_matrix' in model_result['metrics']:
                    cm = model_result['metrics']['confusion_matrix']
                    im = ax.imshow(cm, interpolation='nearest', cmap=plt.cm.Blues)
                    ax.set_title(f'{model_name} 混淆矩阵')

                    # 添加数值标注
                    thresh = cm.max() / 2.
                    for i in range(cm.shape[0]):
                        for j in range(cm.shape[1]):
                            ax.text(j, i, format(cm[i, j], 'd'),
                                   ha="center", va="center",
                                   color="white" if cm[i, j] > thresh else "black")

                    ax.set_ylabel('真实标签')
                    ax.set_xlabel('预测标签')
                    plt.colorbar(im, ax=ax)
                else:
                    ax.text(0.5, 0.5, '混淆矩阵数据不可用', ha='center', va='center', transform=ax.transAxes)
                    ax.set_title(f'{model_name} 混淆矩阵')

            elif chart_type == 'feature_importance':
                # 特征重要性
                if 'feature_importance' in model_result:
                    importance = model_result['feature_importance']
                    if isinstance(importance, dict):
                        features = list(importance.keys())
                        values = list(importance.values())

                        # 排序并取前10个
                        sorted_idx = np.argsort(values)[-10:]
                        features = [features[i] for i in sorted_idx]
                        values = [values[i] for i in sorted_idx]

                        ax.barh(range(len(features)), values)
                        ax.set_yticks(range(len(features)))
                        ax.set_yticklabels(features)
                        ax.set_xlabel('重要性')
                        ax.set_title(f'{model_name} 特征重要性 (Top 10)')
                    else:
                        ax.text(0.5, 0.5, '特征重要性格式不支持', ha='center', va='center', transform=ax.transAxes)
                        ax.set_title(f'{model_name} 特征重要性')
                else:
                    ax.text(0.5, 0.5, '特征重要性数据不可用', ha='center', va='center', transform=ax.transAxes)
                    ax.set_title(f'{model_name} 特征重要性')

            else:
                # 其他图表类型的占位符
                ax.text(0.5, 0.5, f'{chart_type} 图表正在开发中', ha='center', va='center', transform=ax.transAxes)
                ax.set_title(f'{model_name} {chart_type}')

            plt.tight_layout()

            # 将图表嵌入到tkinter中
            canvas = FigureCanvasTkAgg(fig, chart_frame)
            canvas.draw()
            canvas.get_tk_widget().pack(fill='both', expand=True)

            # 添加导航工具栏
            toolbar = NavigationToolbar2Tk(canvas, chart_frame)
            toolbar.update()

            # 添加到notebook
            chart_names = {
                'roc_curve': '📈 ROC曲线',
                'confusion_matrix': '🎯 混淆矩阵',
                'feature_importance': '⭐ 特征重要性',
                'learning_curve': '📚 学习曲线',
                'precision_recall': '🎯 PR曲线',
                'calibration': '⚖️ 校准曲线'
            }

            self.model_chart_notebook.add(chart_frame, text=chart_names.get(chart_type, chart_type))

        except Exception as e:
            self.logger.error(f"创建 {chart_type} 图表时出错: {e}")
            raise
